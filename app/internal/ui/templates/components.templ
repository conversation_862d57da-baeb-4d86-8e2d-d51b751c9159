
package templates
import (
    "strings"
    "github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)


// the list of available signal types for the selected ISN
templ SignalTypeOptions(signalTypes []types.SignalTypeOption) {
	<select
		id="signal-type-slug"
		name="signal-type-slug"
		required
		hx-post="/ui-api/signal-type-version-options"
		hx-target="#sem-ver"
		hx-swap="outerHTML"
		hx-trigger="change"
		hx-include="#isn-slug, this"
		class="form-select"
	>
		<option value="">Select Signal Type...</option>
		for _, signalType := range signalTypes {
			<option value={ signalType.Slug }>{ strings.ReplaceAll(signalType.Slug, "-", " ") }</option>
		}
	</select>
}

templ ServiceAccountOptions(serviceAccounts []types.ServiceAccountOption) {
	// todo toggle button should be a param
	<select
		id="service-account-dropdown"
		name="service-account-dropdown"
		required
		class="form-select"
		hx-get="/ui-api/reissue-btn-state"
		hx-trigger="change"
		hx-target="#reissue-btn-container"
		hx-swap="innerHTML"
		hx-include="this"
	>
		<option value="">Select Service Account...</option>
		for _, account := range serviceAccounts {
			<option value={ account.ClientOrganization + "|" + account.ClientContactEmail }>{ account.ClientOrganization } ({ account.ClientContactEmail })</option>
		}
	</select>
	<!-- Clear alerts when dropdown selection changes -->
	<div hx-get="/ui-api/clear-alerts"
		hx-trigger="change from:#service-account-dropdown"
		hx-target="#reissue-result"
		hx-swap="innerHTML"
		style="display: none;">
	</div>
}

templ UserOptionsNew(users []types.UserOption) {
	<select
		id="account-identifier-nick"
		name="user-dropdown"
		required
		class="form-select"
		hx-swap="innerHTML"
		hx-include="this"
	>
		<option value="">Select User ...</option>
		for _, user := range users {
			<option value={ user.Email + "|" + user.UserRole }>{ user.Email } ({ user.UserRole })</option>
		}
	</select>
}
templ UserOptions(users []types.UserOption) {
	<select
		id="user-dropdown"
		name="user-dropdown"
		required
		class="form-select"
		hx-swap="innerHTML"
		hx-include="this"
	>
		<option value="">Select User ...</option>
		for _, user := range users {
			<option value={ user.Email + "|" + user.UserRole }>{ user.Email } ({ user.UserRole })</option>
		}
	</select>
}

templ UserOptionsGeneratePasswordLink(users []types.UserOption) {
	<select
		id="user-dropdown"
		name="user-dropdown"
		required
		class="form-select"
		hx-get="/ui-api/generate-password-reset-btn-state"
		hx-trigger="change"
		hx-target="#generate-password-reset-btn-container"
		hx-swap="innerHTML"
		hx-include="this"
	>
		<option value="">Select User ...</option>
		for _, user := range users {
			<option value={ user.Email + "|" + user.UserRole }>{ user.Email } ({ user.UserRole })</option>
		}
	</select>
	<!--todo Clear alerts when dropdown selection changes -->
	<div hx-get="/ui-api/clear-alerts"
		hx-trigger="change from:#user-dropdown"
		hx-target="#generate-password-reset-result"
		hx-swap="innerHTML"
		style="display: none;">
	</div>
}
// the list of available versions for the selected signal type
templ SignalTypeVersionOptions(versions []types.VersionOption) {
	<select
		id="sem-ver"
		name="sem-ver"
		required
		class="form-select"
	>
		<option value="">Select Version...</option>
		for _, version := range versions {
			<option value={ version.Version }>{ version.Version }</option>
		}
	</select>
}

templ WarningAlert(message string) {
	<div class="alert alert-warning">
		<svg aria-hidden="true" focusable="false" viewBox="0 0 16 16" width="16" height="16" fill="currentColor" display="inline-block" overflow="visible" style="vertical-align: text-bottom;"><path d="M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path></svg>
		{ message }
	</div>
}

templ ErrorAlert(message string) {
	<div class="alert alert-error">
		{ message }
	</div>
}

templ SuccessAlert(message string) {
	<div class="alert alert-success">
		{ message }
	</div>
}

templ ClearAlerts() {

}

templ SignalMetadataItem(label, value string) {
	<div class="signal-metadata-item">
		<span class="signal-metadata-label">{ label }:</span>
		<div class="signal-metadata-value">{ value }</div>
	</div>
}

templ SignalMetadataSimple(label, value string) {
	<div class="signal-metadata-item">
		<span class="signal-metadata-label">{ label }:</span> { value }
	</div>
}

templ CheckboxField(name, value, label string) {
	<label class="checkbox-field">
		<input type="checkbox" name={ name } value={ value } class="checkbox-input"/>
		<span class="text-sm">{ label }</span>
	</label>
}


