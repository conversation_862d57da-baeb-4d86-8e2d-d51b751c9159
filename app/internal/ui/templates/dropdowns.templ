package templates

import (
    "github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)

/*---------------------------------------------------------------------------------
 * ACCOUNT TYPE SELECTOR
 * select from a dropdown of web user email accounts or service account client IDs
 *---------------------------------------------------------------------------------*/

// Account type selector dropdown
templ AccountTypeSelector() {
    <select
        id="account-type"
        name="account-type"
        required
        class="form-select"
        hx-get="/ui-api/account-identifier-field"
        hx-target="#account-identifier-container"
        hx-swap="outerHTML"
        hx-trigger="change"
        hx-include="this"
    >
        <option value="">Select Account Type...</option>
        <option value="user">User</option>
        <option value="service-account">Service Account</option>
    </select>
}

// Placeholder input field (disabled)
templ AccountIdentifierPlaceholder() {
    <div id="account-identifier-container">
        <input
            id="account-identifier"
            name="account-identifier"
            type="text"
            placeholder="Select account type first..."
            disabled
            class="form-input"
        />
    </div>
}

// User dropdown (replaces placeholder when user is selected)
templ AccountIdentifierUserDropdown(users []types.UserOption) {
    <div id="account-identifier-container">
        <select
            id="account-identifier"
            name="account-identifier"
            required
            class="form-select"
        >
            <option value="">Select User...</option>
            for _, user := range users {
                <option value={ user.Email }>{ user.Email } ({ user.UserRole })</option>
            }
        </select>
    </div>
}

// Service account dropdown 
templ AccountIdentifierServiceAccountDropdown(serviceAccounts []types.ServiceAccountOption) {
    <div id="account-identifier-container">
        <select
            id="account-identifier"
            name="account-identifier"
            required
            class="form-select"
        >
            <option value="">Select Service Account...</option>
            for _, account := range serviceAccounts {
                <option value={ account.ClientContactEmail }>{ account.ClientOrganization } ({ account.ClientContactEmail })</option>
            }
        </select>
    </div>
}

templ IsnAccountPermissionsDropdown() {
    <select
        id="permission"
        name="permission"
        required
        class="form-select"
    >
        <option value="">Select Permission...</option>
        <option value="none">None - Revoke access to the ISN</option>
        <option value="read">Read - Can view signals</option>
        <option value="write">Write - Can create and view signals</option>
    </select>
}