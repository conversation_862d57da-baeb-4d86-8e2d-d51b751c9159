package templates

import (
    "strings"
	"github.com/information-sharing-networks/signalsd/app/internal/ui/types"
)

templ IsnAccountManagementPage(isns []types.IsnOption) {
	@BaseLayout("ISN Account Management") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">ISN Account Management</h1>
			<p class="text-muted mb-6">Grant or revoke access to Information Sharing Networks.</p>

			<div class="card mb-6">
				<div class="card-header">
					<h3 class="card-title">Change ISN permissions for an account</h3>
				</div>
				<div class="card-body">
					<form hx-post="/ui-api/update-isn-account" hx-target="#update-access-result" class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
							<form>
								<div class="form-group">
									<label for="isn-slug" class="form-label">ISN</label>
									<select
										id="isn-slug"
										name="isn-slug"
										required
										class="form-select"
									>
										<option value="">Select ISN...</option>
										for _, isn := range isns {
											<option value={ isn.Slug }>{ strings.ReplaceAll(isn.Slug, "-", " ") }</option>
										}
									</select>
								</div>
								<div class="form-group">
                        			<label for="account-type" class="form-label">Account Type</label>
									@AccountTypeSelector()
								</div>
								
								<div class="form-group">
									<label for="account-identifier" class="form-label">Account</label>
									@AccountIdentifierPlaceholder()
								</div>
								
								<div class="form-group">
									<button type="submit" class="btn btn-primary">Submit</button>
								</div>
							</form>
						</div>
					</form>
				</div>
			</div>

			<div id="update-access-result">
				<!-- Results will appear here -->
			</div>
		</div>
	}	
}
templ IsnAccountManagementPageOld(isns []types.IsnOption) {
	@BaseLayout("ISN Account Management") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">ISN Account Management</h1>
			<p class="text-muted mb-6">Grant or revoke access to Information Sharing Networks.</p>

			<div class="card mb-6">
				<div class="card-header">
					<h3 class="card-title">Change ISN permissions for an account</h3>
				</div>
				<div class="card-body">
					<form hx-post="/ui-api/update-isn-account" hx-target="#update-access-result" class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div class="form-group">
								<label for="isn-slug" class="form-label">ISN</label>
								<select
									id="isn-slug"
									name="isn-slug"
									required
									class="form-select"
								>
									<option value="">Select ISN...</option>
									for _, isn := range isns {
										<option value={ isn.Slug }>{ strings.ReplaceAll(isn.Slug, "-", " ") }</option>
									}
								</select>
							</div>
							<div class="form-group">
								<label for="account-type" class="form-label">Account Type</label>
								<select
									id="account-type"
									name="account-type"
									required
									class="form-select"
									hx-trigger="change"
									hx-target="#account-identifier"
									hx-swap="outerHTML"
									hx-get="/ui-api/account-identifier-field"
								>
									<option value="">Select Type...</option>
									<option value="user">Web User</option>
									<option value="service-account">Service Account</option>
								</select>
							</div>
							<div class="form-group">
								<label for="account-identifier" class="form-label">Account Identifier</label>
								<input
									type="text"
									id="account-identifier"
									name="account-identifier"
									required
									placeholder="Select account type first"
									class="form-input"
									disabled
								/>
							</div>
						</div>
						<div class="form-group">
							<label for="permission" class="form-label">Permission Level</label>
							<select
								id="permission"
								name="permission"
								required
								class="form-select"
							>
								<option value="">Select Permission...</option>
								<option value="none">None - Revoke access to the ISN</option>
								<option value="read">Read - Can view signals</option>
								<option value="write">Write - Can create and view signals</option>
							</select>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary">
								Update Account
							</button>
						</div>
					</form>
				</div>
			</div>

			<div id="update-access-result">
				<!-- Results will appear here -->
			</div>
		</div>
	}
}

// AccountIdentifierField renders the appropriate input field based on account type
templ AccountIdentifierField(accountType string) {
	<div class="form-group" id="account-identifier-field">
		switch accountType {
		case "user2":
			<label for="account-identifier" class="form-label">Account Email</label>
			<input
				type="email"
				id="account-identifier"
				name="account-identifier"
				required
				placeholder="<EMAIL>"
				class="form-input"
			/>
			<label for="account-identifier" class="form-label">Account Email</label>
			<input
				type="email"
				id="account-identifier"
				name="account-identifier"
				required
			/>
		case "user":
			<div class="form-group">
				<label for="account-identifer" class="form-label">Email</label>
				<select
					id="account-identifer"
					hx-get="/ui-api/user-options-new"
					hx-trigger="load"
				>
				</select>
			</div>

		case "service-account":
			<label for="account-identifier" class="form-label">Client ID</label>
			<input
				type="text"
				id="account-identifier"
				required
				placeholder="sa_example-org_abc123"
				class="form-input"
			/>
		default:
			<label for="account-identifier" class="form-label">Account Identifier</label>
			<input
				type="text"
				id="account-identifier"
				name="account-identifier"
				required
				placeholder="Select account type first"
				class="form-input"
				disabled
			/>
		}
	</div>
}
